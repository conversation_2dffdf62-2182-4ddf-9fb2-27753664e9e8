#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import base64
import json
import time
import logging
from datetime import datetime
from urllib import request, error
from pathlib import Path
import argparse
import os
import gzip
import shutil
import urllib.request
import sys

def parse_args():
    parser = argparse.ArgumentParser(description='下载PGO性能分析文件')
    parser.add_argument('--plan-id', type=str, required=True, help='Space平台上的Plan ID')
    parser.add_argument('--output-dir', type=str, default='pgo_files', help='输出目录路径')
    return parser.parse_args()

def main():
    args = parse_args()

    # 1. 获取space_token
    login_url = "https://space.shopee.io/apis/space_auth/v1/basic_login"
    header = {
        "Content-Type": "application/json",
        'Authorization': 'Basic bzJvX3BpcGVsaW5lX2JvdDpzcmNoJnJjbWRAbzJv'
    }

    try:
        req = request.Request(login_url, data={}, headers=header, method="POST")
        with request.urlopen(req) as response:
            data = json.loads(response.read())
            space_token = data.get("token", "")
            logging.info(data)

        if not space_token:
            logging.error("get space_token failed")
            return [], 1

        logging.info(f"get space_token successfully: {space_token}")

        # 2. 获取PGO文件信息
        env = os.getenv('env', 'test')
        if env == "live" or env == "liveish":
            pgo_file_url = f"http://search-pgo.shopeefood.com/api/gateway/v1/mts/auto_profiler/get_profiling_histories?page=1&per_page=1&plan_id={args.plan_id}"
        else:
            pgo_file_url = f"https://space.shopee.io/api/gateway/v1/mts/auto_profiler/get_profiling_histories?page=1&per_page=1&plan_id={args.plan_id}"

        logging.info(f"pgo_file_url: {pgo_file_url}")
        header = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + space_token,
        }

        for attempt in range(0, 30):
            req = request.Request(pgo_file_url, data={}, headers=header, method="GET")
            with request.urlopen(req) as response:
                resp = response.read()
                data = json.loads(resp)
                logging.info(data)
                if data.get("error", -1) != 0:
                    err_msg = data.get("error_msg", "unknown error")
                    logging.error(f"get PGO file error: {err_msg}")
                    if attempt < 29:
                        logging.info("retry get PGO file...")
                        time.sleep(3)
                        continue
                    else:
                        return [], 1

                profiles = process_histories(data.get("data", {}).get("histories", []), args.output_dir)
                return profiles, 0

    except error.HTTPError as e:
        logging.error(f"HTTP请求失败: {e.code} {e.reason}")
    except error.URLError as e:
        logging.error(f"URL请求失败: {e.reason}")
    except Exception as e:
        logging.error(f"发生未知错误: {str(e)}")

    return [], 1

def process_histories(histories, output_dir):
    """处理历史记录并下载文件"""
    profiles = []
    for h in histories:
        try:
            metric_collection_time = h.get("metrics_collection_time", "")
            if not metric_collection_time:
                continue

            # 处理时间格式
            metric_collection_time = metric_collection_time[:-4].strip()
            collection_time = datetime.strptime(
                metric_collection_time, "%Y-%m-%d %H:%M:%S %z"
            )

            # 检查是否过期（超过一周）
            if (time.time() - time.mktime(collection_time.timetuple())) > 604800:
                continue

            # 下载文件
            url = h.get("profiling_data", {}).get("pprof_data_url", "")
            if url and (dest := retrieve_pgo_files_to_local(url, output_dir)):
                profiles.append(dest)

        except Exception as e:
            logging.warning(f"处理记录时出错: {str(e)}")
            continue

    return profiles

def retrieve_pgo_files_to_local(url, output_dir):
    """
    下载并解压PGO分析文件

    Args:
        url (str): 文件下载URL
        output_dir (str): 输出目录路径

    Returns:
        str: 解压后的文件路径，失败返回None
    """
    try:
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)

        # 临时文件名
        temp_gz = os.path.join(output_dir, "temp_profile.pgo.gz")
        final_file = os.path.join(output_dir, "profile.pgo")

        # 下载文件
        logging.info(f"正在下载: {url}")
        urllib.request.urlretrieve(url, temp_gz)

        # 验证文件
        if not os.path.exists(temp_gz) or os.path.getsize(temp_gz) == 0:
            logging.error("下载文件无效")
            return None

        # 解压文件
        logging.info("正在解压文件...")
        with gzip.open(temp_gz, 'rb') as f_in:
            with open(final_file, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)

        # 验证解压文件
        if not os.path.exists(final_file) or os.path.getsize(final_file) == 0:
            logging.error("解压文件无效")
            return None

        return final_file

    except Exception as e:
        logging.error(f"文件处理失败: {str(e)}")
        return None
    finally:
        # 清理临时文件
        if os.path.exists(temp_gz):
            os.remove(temp_gz)

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    profiles, status = main()
    if profiles:
        logging.info(f"成功下载 {len(profiles)} 个PGO文件")
    sys.exit(status)