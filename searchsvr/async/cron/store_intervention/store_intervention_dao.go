package store_intervention

import (
	"context"
	"go.uber.org/zap"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_top_cron"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

type StoreInterventionDao struct {
	db                    *StoreInterventionDB
	storeInterventionDict map[string]*StoreIntervention
	lock                  sync.RWMutex
}

var StoreInterventionDAO *StoreInterventionDao
var once sync.Once

const (
	maxTopInterventionCount = 10
)

func NewStoreInterventionDao(db *StoreInterventionDB) *StoreInterventionDao {
	once.Do(func() {
		storeDict := make(map[string]*StoreIntervention)
		StoreInterventionDAO = &StoreInterventionDao{
			db:                    db,
			storeInterventionDict: storeDict,
		}
	})
	return StoreInterventionDAO
}
func (l *StoreInterventionDao) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("StoreInterventionDao.Run start")
	err := l.BuildStoreInterventionDict(ctx)
	if err != nil {
		_ = metric_reporter.ReportCronJobError(1, "StoreInterventionDao")
	}
	logkit.FromContext(ctx).Info("StoreInterventionDao.Run finish")
}

func (s *StoreInterventionDao) GetAllStoreIntervention(ctx context.Context) ([]*StoreIntervention, error) {
	itemIntentionList, err := s.db.GetAllStoreInterventionFromDB(ctx)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("Get all store intervention failed")
		return nil, err
	}
	return itemIntentionList, nil
}

func (s *StoreInterventionDao) BuildStoreInterventionDict(ctx context.Context) error {
	storeInterventionList, err := s.GetAllStoreIntervention(ctx)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("Get all item intention failed")
		return err
	}
	tempStoreInterventionMap := make(map[string]*StoreIntervention, len(storeInterventionList))
	for i := range storeInterventionList {
		storeIntervention := storeInterventionList[i]
		keywords := strings.Split(strings.Trim(storeIntervention.SearchKeywords, "[]"), ",")
		locationGroupIDs := strings.Split(strings.Trim(storeIntervention.LocationGroupIds, "[]"), ",")

		storeIntervention.InterventionStoreIdList = ParseStringListToUint64List(storeIntervention.InterventionStoreIds, "store_ids")

		storeIntervention.InterventionBrandIdList = ParseStringListToUint64List(storeIntervention.InterventionBrandIds, "brand_ids")

		storeIntervention.InterventionMerchantIdList = ParseStringListToUint64List(storeIntervention.InterventionMerchantIds, "merchant_ids")

		storeIntervention.InterventionStoreTagIdList = ParseStringListToUint64List(storeIntervention.InterventionStoreTagIds, "tag_ids")

		slotsList := ParseStringListToUint64List(storeIntervention.SlotsPosition, "slot_position")
		storeIntervention.SlotsPositionList = make([]uint64, 0, len(slotsList))
		for _, slots := range slotsList {
			if slots >= 1 {
				storeIntervention.SlotsPositionList = append(storeIntervention.SlotsPositionList, slots-1) // admin 的 position 从 1 开始
			}
		}
		sort.Slice(storeIntervention.SlotsPositionList, func(i, j int) bool {
			return storeIntervention.SlotsPositionList[i] < storeIntervention.SlotsPositionList[j]
		})
		keywordList := make([]string, 0)
		locationGroupIDList := make([]string, 0)
		for _, keyword := range keywords {
			key := s.pretreatmentWord(keyword)
			keywordList = append(keywordList, strings.Trim(key, "\""))
		}
		for _, locationGroupID := range locationGroupIDs {
			locationGroupIDList = append(locationGroupIDList, locationGroupID)
		}
		for _, keyword := range keywordList {
			for _, locationGroupID := range locationGroupIDList {
				key := s.BuildKey(keyword, locationGroupID)
				_, ok := tempStoreInterventionMap[key]
				if !ok {
					tempStoreInterventionMap[key] = storeIntervention
				}
			}
		}
	}
	defer s.lock.Unlock()
	s.lock.Lock()
	s.storeInterventionDict = tempStoreInterventionMap
	return nil
}

func (s *StoreInterventionDao) GetStoreIntervention(keyword string, locationGroupIDs []string, traceInfo *traceinfo.TraceInfo) {
	if len(s.storeInterventionDict) == 0 {
		return
	}
	keyword = s.pretreatmentWord(keyword)
	s.lock.RLock()
	tempStoreIntervention := &StoreIntervention{}
	for _, locationGroupID := range locationGroupIDs {
		storeIntervention, ok := s.storeInterventionDict[s.BuildKey(keyword, locationGroupID)]
		if ok && storeIntervention.UpdateTime > tempStoreIntervention.UpdateTime {
			tempStoreIntervention = storeIntervention
		}
	}
	if tempStoreIntervention.Id <= 0 {
		storeIntervention, ok := s.storeInterventionDict[s.BuildKey(keyword, "all")]
		if ok && storeIntervention.UpdateTime > tempStoreIntervention.UpdateTime {
			tempStoreIntervention = storeIntervention
		}
	}
	s.lock.RUnlock()
	if tempStoreIntervention.Id > 0 {

		traceInfo.OptIntervention.IsStoreInterventionRecall = true
		traceInfo.OptIntervention.InterventionRecallMerchantID = tempStoreIntervention.InterventionMerchantIdList
		traceInfo.OptIntervention.InterventionRecallBrandID = tempStoreIntervention.InterventionBrandIdList
		traceInfo.OptIntervention.InterventionRecallStoreIDList = tempStoreIntervention.InterventionStoreIdList
		traceInfo.OptIntervention.InterventionRecallStoreTagID = tempStoreIntervention.InterventionStoreTagIdList
		traceInfo.OptIntervention.SlotsPosition = tempStoreIntervention.SlotsPositionList
		traceInfo.OptIntervention.InterventionType = tempStoreIntervention.InterventionType
		traceInfo.OptIntervention.IsRotateMerchants = false
		traceInfo.OptIntervention.IsNewMerchantsOnly = false
		if tempStoreIntervention.IsNewMerchantsOnly == 1 {
			traceInfo.OptIntervention.IsNewMerchantsOnly = true
		}
		if tempStoreIntervention.IsRotateMerchants == 1 {
			traceInfo.OptIntervention.IsRotateMerchants = true
		}

		interventionTexts := strings.Split(strings.Trim(tempStoreIntervention.InterventionTexts, "[]"), ",")
		if len(interventionTexts) > 0 {
			textMap := make(map[string]bool)
			var mergedTopKeywords []string
			for _, text := range interventionTexts {
				if text == "" {
					continue
				}
				newText := strings.ToLower(util2.RemoveAllPunctAndTrim(text))
				mergedTopKeywords = append(mergedTopKeywords, newText)
				textMap[newText] = true
			}
			// 合并算法置顶词
			algoTopQuery := GetTopQueryFromAlgoS3(traceInfo)
			for _, algoText := range algoTopQuery {
				if _, ok := textMap[algoText]; !ok && len(mergedTopKeywords) < maxTopInterventionCount {
					mergedTopKeywords = append(mergedTopKeywords, algoText)
				}
			}
			traceInfo.OptIntervention.QueryStoreTop = mergedTopKeywords
			if len(traceInfo.OptIntervention.QueryStoreTop) > 0 {
				traceInfo.IsStoreTopHit = true
				traceInfo.StoreTopKeyword = strings.Join(traceInfo.OptIntervention.QueryStoreTop, ",")
			}
		}
		// 只要置顶的query不为空,则视为置顶召回
		if len(traceInfo.OptIntervention.QueryStoreTop) > 0 {
			traceInfo.OptIntervention.InterventionType = traceinfo.InterventionTypeTop1
		}
	}
}

func (s *StoreInterventionDao) BuildKey(keyword, locationGroupID string) string {
	return keyword + "_" + locationGroupID
}

var punctuationRegex = regexp.MustCompile("[[:punct:]]")

func (s *StoreInterventionDao) pretreatmentWord(word string) string {
	normText := punctuationRegex.ReplaceAllString(word, "")
	normText = strings.Trim(normText, " ")
	return strings.ToLower(normText)
}

func GetTopQueryFromAlgoS3(traceInfo *traceinfo.TraceInfo) []string {
	var mergedTopKeywords []string
	// 仅获取算法S3添加的置顶词，MySQL 部分置顶词表已经在干预表内
	algoVersion := traceInfo.ABTestGroup.GetTopInterventionDictVersion()
	algoTopKeywords, isAlgoTopHit := store_top_cron.AlgoTopIntervention.GetAlgoTopIntervention(algoVersion, traceInfo.QueryKeyword)
	if isAlgoTopHit {
		for i := 0; i < len(algoTopKeywords) && len(mergedTopKeywords) < maxTopInterventionCount; i++ {
			keyword := strings.ToLower(util2.RemoveAllPunctAndTrim(algoTopKeywords[i]))
			mergedTopKeywords = append(mergedTopKeywords, keyword)
		}
	}
	return mergedTopKeywords
}

func ParseStringListToUint64List(strList, strType string) []uint64 {
	IdsStrList := strings.Split(strings.Trim(strList, "[]"), ",")
	retUint64List := make([]uint64, 0, len(IdsStrList))
	if len(IdsStrList) > 0 {
		for _, pos := range IdsStrList {
			if len(pos) == 0 {
				continue
			}
			val, err := strconv.ParseUint(pos, 10, 64)
			if err != nil {
				logkit.Error("intervention ids parse fail", zap.String("Type", strType), zap.String("val", strList), zap.String("pos", pos))
				continue
			}
			retUint64List = append(retUint64List, val)
		}
	}
	return retUint64List
}
