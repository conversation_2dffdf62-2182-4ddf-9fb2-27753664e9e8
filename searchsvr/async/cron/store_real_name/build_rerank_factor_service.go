package store_real_name

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"go.uber.org/zap"
)

const StoreRealNameFileName = "search_extract_real_store_name"

type BuildStoreRealNameService struct {
	s3Client               s3.S3Service
	queryStoreRealNameDict *QueryStoreRealNameDict
}

func NewBuildStoreRealNameService(s3Client s3.S3Service, StoreRealNameDict *QueryStoreRealNameDict) *BuildStoreRealNameService {
	return &BuildStoreRealNameService{
		s3Client:               s3Client,
		queryStoreRealNameDict: StoreRealNameDict,
	}
}

var currentTimestamp int64
var isFirstInit = true

func (b *BuildStoreRealNameService) InitOrUpdate() {
	ctx := context.Background()
	filePwd, err := b.DownloadFile(getStoreRealNameFileName())
	if err != nil {
		return
	}
	b.queryStoreRealNameDict.BuildQueryStoreRealNameDict(ctx, filePwd)
}

func (b *BuildStoreRealNameService) Run() {
	fileName := getStoreRealNameFileName()
	logkit.Info("BuildStoreRealNameService start", zap.String("filename", fileName))
	modifyTime, err := b.s3Client.GetLastModified(fileName)
	if err != nil {
		logkit.Error("BuildStoreRealNameService Connect s3 client failed", logkit.String("error", err.Error()), logkit.String("fileName", fileName))
		return
	}
	if modifyTime < 1 {
		logkit.Error("BuildStoreRealNameService Get file last modified failed", logkit.String("fileName", fileName))
		return
	}
	if isFirstInit {
		isFirstInit = false
		currentTimestamp = modifyTime
		b.InitOrUpdate()
		logkit.Info("BuildStoreRealNameService fist init done", zap.String("filename", fileName))
		return
	}
	if modifyTime > currentTimestamp {
		currentTimestamp = modifyTime
		logkit.Info("BuildStoreRealNameService update...")
		b.InitOrUpdate()
	}
	logkit.Info("BuildStoreRealNameService done", zap.String("filename", fileName))
}

func (b *BuildStoreRealNameService) DownloadFile(fileName string) (string, error) {
	err := b.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("BuildStoreRealNameService Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}

// vn_test_factors
func getStoreRealNameFileName() string {
	return StoreRealNameFileName + "_" + env.GetCID() + "_" + util.GetEnvLiveish(false) + ".txt"
}
