package store_real_name

import (
	"bufio"
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"github.com/coocood/freecache"
	"io"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"go.uber.org/zap"
)

type QueryStoreRealNameDict struct {
	Dict *freecache.Cache
}

var StoreRealNameDict *QueryStoreRealNameDict

const Separator = "###"
const sepNum = 4

var queryStoreRealNameOnce sync.Once

func NewQueryStoreRealNameDao() *QueryStoreRealNameDict {
	cacheSize := 256
	if apollo.SearchApolloCfg.StoreRealNameCacheSize != 0 {
		cacheSize = apollo.SearchApolloCfg.StoreRealNameCacheSize
	}
	cacheSize = cacheSize * 1024 * 1024 // Convert MB to bytes
	queryStoreRealNameOnce.Do(func() {
		StoreRealNameDict = &QueryStoreRealNameDict{
			Dict: localcache.InitFreeCache("StoreRealNameDict", cacheSize),
		}
	})
	return StoreRealNameDict
}

func (d *QueryStoreRealNameDict) BuildQueryStoreRealNameDict(ctx context.Context, filePwd string) {
	//准备读取文件
	startTime := time.Now()
	fs, err := os.Open(filePwd)
	if err != nil {
		_ = metric_reporter.ReportCronJobError(1, "load_file_"+filePwd+"_error")
		logkit.FromContext(ctx).WithError(err).Error("BuildQueryStoreRealNameDict Failed to open file",
			logkit.String("file path", filePwd))
		return
	}
	defer fs.Close()
	br := bufio.NewReader(fs)
	count := 0
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		str := string(a)
		// storeId###storeName###realName###branchName
		splits := strings.Split(str, Separator)
		if len(splits) != sepNum {
			logkit.Error("BuildQueryStoreRealNameDict line format invalid", zap.String("str", str), zap.String("filename", filePwd))
			continue
		}
		count++
		storeId, parseErr := strconv.ParseInt(splits[0], 10, 64)
		if parseErr != nil {
			_ = metric_reporter.ReportCronJobError(1, filePwd+"_parse_storeId_error")
			logkit.Error("BuildQueryStoreRealNameDict parse storeId failed", zap.String("storeId", splits[0]), zap.Error(parseErr))
			continue
		}
		storeIdBytes := util.IntToBytes(storeId)
		nameStr := strings.Join(splits[1:], Separator)
		setErr := d.Dict.Set(storeIdBytes, []byte(nameStr), 0)
		if setErr != nil {
			_ = metric_reporter.ReportCronJobError(1, filePwd+"_set_cache_error")
			logkit.Error("BuildQueryStoreRealNameDict set cache failed", zap.String("storeId", splits[0]), zap.String("nameStr", nameStr), zap.Error(setErr))
			continue
		}
	}
	logkit.Info("BuildQueryStoreRealNameDict finished", zap.String("cost", time.Since(startTime).String()), zap.Int("size", count))
}
