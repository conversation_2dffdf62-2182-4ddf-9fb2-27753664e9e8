// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"database/sql"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/elastic/config"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/redis/client"
	config2 "git.garena.com/shopee/o2o-intelligence/common/common-lib/redis/config"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/store_blacklist"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/pcfactor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/predefined_keyword"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_ctr_cvr"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_intervention"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_rank_factor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_real_name"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_top_cron"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/unsettled_store"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/user_level"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_brand_protect"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_store_incubation"
	config3 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/handler"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/rediscache"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// Injectors from wire.go:

func InitializeAsyncTask(es *config.Elastic, redis *config2.Redis, conf *config3.GDSKafka, gdsWatchSchema config3.WatchSchema, dataStreamMysql *config3.DataStreamMysql, dataPlatformMysql *config3.DataPlatformMysql, s3Config s3.S3Config, s3FileName *config3.S3FileName) *async.TaskPool {
	dataStreamMysqlDB := config3.NewDataStreamMysqlDB(dataStreamMysql)
	predefinedKeywordDB := predefined_keyword.NewPredefinedKeywordDB(dataStreamMysqlDB)
	predefinedKeywordDao := predefined_keyword.NewPredefinedKeywordDao(predefinedKeywordDB)
	predefinedKeywordCache := predefined_keyword.NewPredefinedKeywordCache()
	loadUserSearchKeywordJob := predefined_keyword.NewLoadUserSearchKeywordJob(predefinedKeywordDao, predefinedKeywordCache)
	s3Service := s3.NewS3Service(s3Config)
	algoTopInterventionDao := store_top_cron.NewAlgoTopInterventionDao()
	algoTopInterventionService := store_top_cron.NewAlgoTopInterventionService(s3Service, s3FileName, algoTopInterventionDao)
	loadStoreTopJob := store_top_cron.NewReloadStoreTopJob(algoTopInterventionService)
	storeInterventionDB := store_intervention.NewStoreInterventionDB(dataStreamMysqlDB)
	storeInterventionDao := store_intervention.NewStoreInterventionDao(storeInterventionDB)
	queryUnsettledStoreDict := unsettled_store.NewQueryUnsettledStoreDao()
	redisClient := client.NewV9(redis)
	qpResultRedis := rediscache.NewQPResultRedis(redisClient)
	qpService := qp.NewQPService(qpResultRedis)
	buildUnsettledStoreService := unsettled_store.NewBuildUnsettledStoreService(s3Service, queryUnsettledStoreDict, s3FileName, qpService)
	buildUnsettledStoreServiceS3Job := unsettled_store.NewBuildUnsettledStoreServiceS3Job(buildUnsettledStoreService)
	storeOfflineDao := store_ctr_cvr.NewStoreCtrCvrDao()
	buildStoreCtrCvrService := store_ctr_cvr.NewBuildStoreCtrCvrService(s3Service, storeOfflineDao, s3FileName)
	buildStoreCtrCvrServiceS3Job := store_ctr_cvr.NewBuildStoreCtrCvrServiceS3Job(buildStoreCtrCvrService)
	levelDao := user_level.NewLevelDict()
	buildUserLevelService := user_level.NewBuildUserLevelService(s3Service, levelDao, s3FileName)
	buildUserLevelJob := user_level.NewBuildStoreCtrCvrServiceS3Job(buildUserLevelService)
	queryStoreRealNameDict := store_real_name.NewQueryStoreRealNameDao()
	buildStoreRealNameService := store_real_name.NewBuildStoreRealNameService(s3Service, queryStoreRealNameDict)
	buildStoreRealNameServiceS3Job := store_real_name.NewBuildStoreRealNameServiceS3Job(buildStoreRealNameService)
	charMapping := vn_char_mapping_job.NewCharMappingInstance()
	buildCharMappingService := vn_char_mapping_job.NewBuildCharMappingService(s3Config, charMapping)
	storeIncubationDict := model.NewStoreIncubationDict()
	buildStoreIncubationJob := vn_store_incubation.NewBuildStoreIncubationJob(s3Service, storeIncubationDict)
	pcFactorDao := pcfactor.NewPcFactorDict()
	buildPcFactorService := pcfactor.NewBuildPcFactorService(s3Service, pcFactorDao, s3FileName)
	buildPcFactorJob := pcfactor.NewBuildPcFactorServiceS3Job(buildPcFactorService)
	buildStoreRankFactor := store_rank_factor.NewStoreRankFactorLoadPlugin(s3Service)
	buildDefaultStoreRankFactor := store_rank_factor.NewDefaultStoreRankFactorLoadPlugin(s3Service)
	dataPlatformMysqlDB := config3.NewDataPlatformMysqlDB(dataPlatformMysql)
	vnBrandProtectionKeywordsDB := vn_brand_protection_keywords.NewVnBrandProtectionKeywordsDB(dataPlatformMysqlDB)
	storeDB := vn_brand_protection_keywords.NewStoresDB(dataPlatformMysqlDB)
	vnBrandProtectionKeywordsDao := vn_brand_protection_keywords.NewVnBrandProtectionKeywordsDao(vnBrandProtectionKeywordsDB, storeDB)
	buildLoadVnBrandProtectionKeywordJob := vn_brand_protection_keywords.NewLoadVnBrandProtectionKeywordJob(vnBrandProtectionKeywordsDao)
	cronCron := cron.NewCron(loadUserSearchKeywordJob, loadStoreTopJob, storeInterventionDao, buildUnsettledStoreServiceS3Job, buildStoreCtrCvrServiceS3Job, buildUserLevelJob, buildStoreRealNameServiceS3Job, buildCharMappingService, buildStoreIncubationJob, buildPcFactorJob,
		buildStoreRankFactor, buildDefaultStoreRankFactor, buildLoadVnBrandProtectionKeywordJob)
	taskPool := async.NewTaskPool(cronCron)
	return taskPool
}

func InitializeReloadStoreTop(s3Config s3.S3Config, s3FileName *config3.S3FileName) *store_top_cron.LoadStoreTopJob {
	s3Service := s3.NewS3Service(s3Config)
	algoTopInterventionDao := store_top_cron.NewAlgoTopInterventionDao()
	algoTopInterventionService := store_top_cron.NewAlgoTopInterventionService(s3Service, s3FileName, algoTopInterventionDao)
	loadStoreTopJob := store_top_cron.NewReloadStoreTopJob(algoTopInterventionService)
	return loadStoreTopJob
}

func InitializeStoreIntervention(mysql *config3.DataStreamMysql) *store_intervention.StoreInterventionDao {
	dataStreamMysqlDB := config3.NewDataStreamMysqlDB(mysql)
	storeInterventionDB := store_intervention.NewStoreInterventionDB(dataStreamMysqlDB)
	storeInterventionDao := store_intervention.NewStoreInterventionDao(storeInterventionDB)
	return storeInterventionDao
}

func InitializeReloadNotSettledStore(redis *config2.Redis, s3Config s3.S3Config, s3FileName *config3.S3FileName) *unsettled_store.BuildUnsettledStoreServiceS3Job {
	s3Service := s3.NewS3Service(s3Config)
	queryUnsettledStoreDict := unsettled_store.NewQueryUnsettledStoreDao()
	redisClient := client.NewV9(redis)
	qpResultRedis := rediscache.NewQPResultRedis(redisClient)
	qpService := qp.NewQPService(qpResultRedis)
	buildUnsettledStoreService := unsettled_store.NewBuildUnsettledStoreService(s3Service, queryUnsettledStoreDict, s3FileName, qpService)
	buildUnsettledStoreServiceS3Job := unsettled_store.NewBuildUnsettledStoreServiceS3Job(buildUnsettledStoreService)
	return buildUnsettledStoreServiceS3Job
}

func InitializeReloadStoreCtrCvr(s3Config s3.S3Config, s3FileName *config3.S3FileName) *store_ctr_cvr.BuildStoreCtrCvrServiceS3Job {
	s3Service := s3.NewS3Service(s3Config)
	storeOfflineDao := store_ctr_cvr.NewStoreCtrCvrDao()
	buildStoreCtrCvrService := store_ctr_cvr.NewBuildStoreCtrCvrService(s3Service, storeOfflineDao, s3FileName)
	buildStoreCtrCvrServiceS3Job := store_ctr_cvr.NewBuildStoreCtrCvrServiceS3Job(buildStoreCtrCvrService)
	return buildStoreCtrCvrServiceS3Job
}

func InitializeUserLevel(s3Config s3.S3Config, s3FileName *config3.S3FileName) *user_level.BuildUserLevelJob {
	s3Service := s3.NewS3Service(s3Config)
	levelDao := user_level.NewLevelDict()
	buildUserLevelService := user_level.NewBuildUserLevelService(s3Service, levelDao, s3FileName)
	buildUserLevelJob := user_level.NewBuildStoreCtrCvrServiceS3Job(buildUserLevelService)
	return buildUserLevelJob
}

func InitializeStoreRealNameJob(s3Config s3.S3Config) *store_real_name.BuildStoreRealNameServiceS3Job {
	s3Service := s3.NewS3Service(s3Config)
	queryStoreRealNameDict := store_real_name.NewQueryStoreRealNameDao()
	buildStoreRealNameService := store_real_name.NewBuildStoreRealNameService(s3Service, queryStoreRealNameDict)
	buildStoreRealNameServiceS3Job := store_real_name.NewBuildStoreRealNameServiceS3Job(buildStoreRealNameService)
	return buildStoreRealNameServiceS3Job
}

func InitializeCharMappingJob(s3Config s3.S3Config) *vn_char_mapping_job.BuildCharMappingService {
	charMapping := vn_char_mapping_job.NewCharMappingInstance()
	buildCharMappingService := vn_char_mapping_job.NewBuildCharMappingService(s3Config, charMapping)
	return buildCharMappingService
}

func InitalizeStoreIncubation(s3Config s3.S3Config) *vn_store_incubation.BuildStoreIncubationJob {
	s3Service := s3.NewS3Service(s3Config)
	storeIncubationDict := model.NewStoreIncubationDict()
	buildStoreIncubationJob := vn_store_incubation.NewBuildStoreIncubationJob(s3Service, storeIncubationDict)
	return buildStoreIncubationJob
}

func InitializePcFactor(s3Config s3.S3Config, s3FileName *config3.S3FileName) *pcfactor.BuildPcFactorJob {
	s3Service := s3.NewS3Service(s3Config)
	pcFactorDao := pcfactor.NewPcFactorDict()
	buildPcFactorService := pcfactor.NewBuildPcFactorService(s3Service, pcFactorDao, s3FileName)
	buildPcFactorJob := pcfactor.NewBuildPcFactorServiceS3Job(buildPcFactorService)
	return buildPcFactorJob
}

func InitializeLoadVnBrandProtectionKeywordJob(dataPlatformMysql *config3.DataPlatformMysql) *vn_brand_protection_keywords.LoadVnBrandProtectionKeywordJob {
	dataPlatformMysqlDB := config3.NewDataPlatformMysqlDB(dataPlatformMysql)
	vnBrandProtectionKeywordsDB := vn_brand_protection_keywords.NewVnBrandProtectionKeywordsDB(dataPlatformMysqlDB)
	storeDB := vn_brand_protection_keywords.NewStoresDB(dataPlatformMysqlDB)
	vnBrandProtectionKeywordsDao := vn_brand_protection_keywords.NewVnBrandProtectionKeywordsDao(vnBrandProtectionKeywordsDB, storeDB)
	buildLoadVnBrandProtectionKeywordJob := vn_brand_protection_keywords.NewLoadVnBrandProtectionKeywordJob(vnBrandProtectionKeywordsDao)
	return buildLoadVnBrandProtectionKeywordJob
}

func InitializeStoreBlacklistJob(dataPlatformMysql *config3.DataPlatformMysql) {
	dataPlatformMysqlDB := config3.NewDataPlatformMysqlDB(dataPlatformMysql)
	db := (*sql.DB)(dataPlatformMysqlDB)
	store_blacklist.Init(db)
	return
}

// wire.go:

func InitializeHandler() *handler.SearchHandler {
	return &handler.SearchHandler{}
}
