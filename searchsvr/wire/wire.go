//go:build wireinject
// +build wireinject

package wire

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"github.com/google/wire"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_store_incubation"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/rediscache"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_real_name"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/user_level"
	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_ctr_cvr"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/unsettled_store"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"

	predefined_keyword2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/predefined_keyword"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_intervention"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_top_cron"

	esconfig "git.garena.com/shopee/o2o-intelligence/common/common-lib/elastic/config"
	redisclient "git.garena.com/shopee/o2o-intelligence/common/common-lib/redis/client"
	redisconfig "git.garena.com/shopee/o2o-intelligence/common/common-lib/redis/config"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/pcfactor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/handler"
)

func InitializeHandler() *handler.SearchHandler {
	return &handler.SearchHandler{}
}

func InitializeAsyncTask(
	es *esconfig.Elastic,
	redis *redisconfig.Redis,
	conf *config2.GDSKafka,
	gdsWatchSchema config2.WatchSchema, dataStreamMysql *config2.DataStreamMysql, s3Config s3.S3Config, s3FileName *config2.S3FileName) *async.TaskPool {
	wire.Build(
		redisclient.New,
		cron.NewCron,
		async.NewTaskPool,
		predefined_keyword2.NewPredefinedKeywordCache,
		predefined_keyword2.NewLoadUserSearchKeywordJob,
		vn_brand_protect.New,
		predefined_keyword2.NewPredefinedKeywordDao,
		predefined_keyword2.NewPredefinedKeywordDB,
		store_top_cron.NewAlgoTopInterventionDao,
		store_top_cron.NewAlgoTopInterventionService,
		store_top_cron.NewReloadStoreTopJob,
		store_intervention.NewStoreInterventionDao,
		store_intervention.NewStoreInterventionDB,
		config2.NewDataStreamMysqlDB,
		config2.NewDataPlatformMysqlDB,

		s3.NewS3Service,
		rediscache.NewQPResultRedis,
		qp.NewQPService,
		unsettled_store.NewQueryUnsettledStoreDao,
		unsettled_store.NewBuildUnsettledStoreService,
		unsettled_store.NewBuildUnsettledStoreServiceS3Job,
		store_ctr_cvr.NewStoreCtrCvrDao,
		store_ctr_cvr.NewBuildStoreCtrCvrService,
		store_ctr_cvr.NewBuildStoreCtrCvrServiceS3Job,
		user_level.NewBuildUserLevelService,
		user_level.NewLevelDict,
		user_level.NewBuildStoreCtrCvrServiceS3Job,
		store_real_name.NewQueryStoreRealNameDao,
		model.NewStoreIncubationDict,
		store_real_name.NewBuildStoreRealNameService,
		store_real_name.NewBuildStoreRealNameServiceS3Job,
		vn_char_mapping_job.NewCharMappingInstance,
		vn_char_mapping_job.NewBuildCharMappingService,
		vn_store_incubation.NewBuildStoreIncubationJob,
		uefactor.NewBuildUeFactorService,
		uefactor.NewUeFactorDict,
		uefactor.NewBuildUeFactorServiceS3Job,
		pcfactor.NewBuildPcFactorServiceS3Job,
	)
	return &async.TaskPool{}
}

func InitializeReloadStoreTop(s3Config s3.S3Config, s3FileName *config2.S3FileName) *store_top_cron.LoadStoreTopJob {
	wire.Build(
		s3.NewS3Service,
		store_top_cron.NewAlgoTopInterventionDao,
		store_top_cron.NewAlgoTopInterventionService,
		store_top_cron.NewReloadStoreTopJob,
	)
	return &store_top_cron.LoadStoreTopJob{}
}
func InitializeStoreIntervention(mysql *config2.DataStreamMysql) *store_intervention.StoreInterventionDao {
	wire.Build(
		store_intervention.NewStoreInterventionDao,
		store_intervention.NewStoreInterventionDB,
		config2.NewDataStreamMysqlDB,
	)
	return &store_intervention.StoreInterventionDao{}
}

func InitializeReloadNotSettledStore(redis *redisconfig.Redis, s3Config s3.S3Config, s3FileName *config2.S3FileName) *unsettled_store.BuildUnsettledStoreServiceS3Job {
	wire.Build(
		redisclient.New,
		s3.NewS3Service,
		rediscache.NewQPResultRedis,
		qp.NewQPService,
		unsettled_store.NewQueryUnsettledStoreDao,
		unsettled_store.NewBuildUnsettledStoreService,
		unsettled_store.NewBuildUnsettledStoreServiceS3Job,
	)
	return &unsettled_store.BuildUnsettledStoreServiceS3Job{}
}

func InitializeReloadStoreCtrCvr(s3Config s3.S3Config, s3FileName *config2.S3FileName) *store_ctr_cvr.BuildStoreCtrCvrServiceS3Job {
	wire.Build(
		s3.NewS3Service,
		store_ctr_cvr.NewStoreCtrCvrDao,
		store_ctr_cvr.NewBuildStoreCtrCvrService,
		store_ctr_cvr.NewBuildStoreCtrCvrServiceS3Job,
	)
	return &store_ctr_cvr.BuildStoreCtrCvrServiceS3Job{}
}

func InitializeUserLevel(s3Config s3.S3Config, s3FileName *config2.S3FileName) *user_level.BuildUserLevelJob {
	wire.Build(
		s3.NewS3Service,
		user_level.NewBuildUserLevelService,
		user_level.NewLevelDict,
		user_level.NewBuildStoreCtrCvrServiceS3Job,
	)
	return &user_level.BuildUserLevelJob{}
}

func InitializeStoreRealNameJob(s3Config s3.S3Config) *store_real_name.BuildStoreRealNameServiceS3Job {
	wire.Build(
		s3.NewS3Service,
		store_real_name.NewQueryStoreRealNameDao,
		store_real_name.NewBuildStoreRealNameService,
		store_real_name.NewBuildStoreRealNameServiceS3Job,
	)
	return &store_real_name.BuildStoreRealNameServiceS3Job{}
}

func InitializeUeFactor(s3Config s3.S3Config, s3FileName *config2.S3FileName) *uefactor.BuildUeFactorJob {
	wire.Build(
		s3.NewS3Service,
		uefactor.NewBuildUeFactorService,
		uefactor.NewUeFactorDict,
		uefactor.NewBuildUeFactorServiceS3Job,
	)
	return &uefactor.BuildUeFactorJob{}
}

func InitializeCharMappingJob(s3Config s3.S3Config) *vn_char_mapping_job.BuildCharMappingService {
	wire.Build(
		vn_char_mapping_job.NewCharMappingInstance,
		vn_char_mapping_job.NewBuildCharMappingService,
	)
	return &vn_char_mapping_job.BuildCharMappingService{}
}

func InitalizeStoreIncubation(s3Config s3.S3Config) *vn_store_incubation.BuildStoreIncubationJob {
	wire.Build(
		s3.NewS3Service,
		model.NewStoreIncubationDict,
		vn_store_incubation.NewBuildStoreIncubationJob,
	)
	return &vn_store_incubation.BuildStoreIncubationJob{}
}

func InitializePcFactor(s3Config s3.S3Config, s3FileName *config2.S3FileName) *pcfactor.BuildPcFactorJob {
	wire.Build(
		s3.NewS3Service,
		pcfactor.NewBuildPcFactorService,
		pcfactor.NewPcFactorDict,
		pcfactor.NewBuildPcFactorServiceS3Job,
	)
	return &pcfactor.BuildPcFactorJob{}
}
