package util

import (
	"golang.org/x/text/unicode/norm"
	"math"
	"reflect"
	"regexp"
	"strings"
	"unicode"

	"git.garena.com/shopee/feed/comm_lib/env"
	cid2 "git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	asciiFolding "github.com/hkulekci/ascii-folding"
)

var punctReg = regexp.MustCompile("[[:punct:]]") // 匹配所有标点符号
func TextPreprocessing(s string) string {
	if len(s) == 0 {
		return s
	}
	retStr := punctReg.ReplaceAllString(s, "")
	retStr = strings.Trim(retStr, " ")
	retStr = strings.ToLower(retStr)
	return retStr
}

var RemoveExtraSpaceReg = regexp.MustCompile(`\s{2,}`) // 两个及以上空格的正则
// 删除字符串中的多余空格，有多个空格时，仅保留一个空格
func RemoveExtraSpace(s string) string {
	if len(s) == 0 {
		return s
	}
	return RemoveExtraSpaceReg.ReplaceAllString(s, " ")
}

// 删除所有标点符号和前后空格
func RemoveAllPunctAndTrim(s string) string {
	if len(s) == 0 {
		return s
	}
	retStr := punctReg.ReplaceAllString(s, "")
	return strings.Trim(retStr, " ")
}

// 标点符号用空格替换，前后空格去掉（中间多个空格仍会保留，例如:"gà rán kfc - le van sy" => gà rán kfc   le van sy）
func ReplaceAllPunctWithSpaceAndTrim(s string) string {
	if len(s) == 0 {
		return s
	}
	retStr := punctReg.ReplaceAllString(s, " ")
	return strings.Trim(retStr, " ")
}

func ToInterfaceSlice(slice interface{}) []interface{} {
	s := reflect.ValueOf(slice)

	ret := make([]interface{}, s.Len())
	for i := 0; i < s.Len(); i++ {
		ret[i] = s.Index(i).Interface()
	}
	return ret
}

func CheckLonLat(lon, lat float64) bool {
	if lon > 180.0 || lon < -180.0 || lat > 90.0 || lat < -90.0 {
		return false
	}
	return true
}

func IsAsciiTerms(keyword string) bool {
	asciiKeyword := asciiFolding.Fold(keyword)
	return asciiKeyword == keyword
}

func ToAscii(keyword string) string {
	if cid2.IsVN() == false {
		return keyword
	}
	if len(keyword) == 0 {
		return keyword
	}
	return asciiFolding.Fold(keyword)
}

var RegexEmoji = regexp.MustCompile("^[(.\\s]?[+]?[).\\s]?[0-9,-]{8,15}$")
var RegexSpace = regexp.MustCompile("\\s+")

func StandardizeSearchString(query string) string {
	query = RegexSpace.ReplaceAllString(strings.ToLower(query), " ")
	return removeEmoji(strings.TrimSpace(query))
}

func removeEmoji(query string) string {
	return RegexEmoji.ReplaceAllString(query, "")
}

func GetTokenLen(queryTerms string) int {
	if len(queryTerms) == 0 {
		return 0
	}
	l := 0
	tokens := strings.Split(queryTerms, " ")
	for _, token := range tokens {
		if len(token) > 0 {
			l++
		}
	}
	return l
}

// 归一化处理：去越南语调(vn)，大写转小写，去特殊符号且多个空格保留一个
func StringNormalize(str string) string {
	if len(str) == 0 {
		return str
	}
	if env.GetCID() == cid2.VN {
		str = ToAscii(str)
	}
	return RemoveExtraSpace(strings.ToLower(ReplaceAllPunctWithSpaceAndTrim(str)))
}

// 计算字符串之间term 匹配分数，注意调用前入口需要提前做string归一化
func CountTermMatchScore(src, dest string) uint32 {
	if len(src) == 0 || len(dest) == 0 {
		return 0
	}
	srcs := strings.Split(src, " ")
	dests := strings.Split(dest, " ")
	matchFlag := make([]uint8, len(dests), len(dests))
	var score uint32
	for _, s := range srcs {
		for index, d := range dests {
			// 重复的term要单独计算
			if matchFlag[index] == 1 {
				continue
			}
			if s == d {
				score++
				matchFlag[index] = 1
				break
			}
		}
	}
	return score
}

// 计算匹配阈值
func CountTermMatchThread(branchName string) uint32 {
	if len(branchName) == 0 {
		return 0
	}
	ss := strings.Split(branchName, " ")
	bl := uint32(len(ss))
	if bl <= 2 {
		return bl
	}
	thread := uint32(math.Ceil(float64(bl) * 0.6))
	return thread
}

// 去掉标点符号、统一小写后，按照空格切分
func TextTerms(str string) map[string]struct{} {
	if len(str) == 0 {
		return map[string]struct{}{}
	}
	// 1. 使用正则表达式替换所有标点符号为一个空格
	str = punctReg.ReplaceAllString(str, " ")
	// 2. 将字符串转为小写
	str = strings.ToLower(str)
	// 3. 合并多余的空格，按空格切分字符串
	ss := strings.Fields(str)
	terms := make(map[string]struct{}, len(ss))
	for _, s := range ss {
		terms[s] = struct{}{}
	}
	return terms
}
func ToAsciiWithMapping(text string) string {
	replacer := strings.NewReplacer(
		"Đ", "D", "đ", "d",
		"á", "a", "à", "a", "ả", "a", "ã", "a", "ạ", "a",
		"â", "a", "ấ", "a", "ầ", "a", "ẩ", "a", "ẫ", "a", "ậ", "a",
		"ă", "a", "ắ", "a", "ằ", "a", "ẳ", "a", "ẵ", "a", "ặ", "a",
		"é", "e", "è", "e", "ẻ", "e", "ẽ", "e", "ẹ", "e",
		"ê", "e", "ế", "e", "ề", "e", "ể", "e", "ễ", "e", "ệ", "e",
		"í", "i", "ì", "i", "ỉ", "i", "ĩ", "i", "ị", "i",
		"ó", "o", "ò", "o", "ỏ", "o", "õ", "o", "ọ", "o",
		"ô", "o", "ố", "o", "ồ", "o", "ổ", "o", "ỗ", "o", "ộ", "o",
		"ơ", "o", "ớ", "o", "ờ", "o", "ở", "o", "ỡ", "o", "ợ", "o",
		"ú", "u", "ù", "u", "ủ", "u", "ũ", "u", "ụ", "u",
		"ư", "u", "ứ", "u", "ừ", "u", "ử", "u", "ữ", "u", "ự", "u",
		"ý", "y", "ỳ", "y", "ỷ", "y", "ỹ", "y", "ỵ", "y",
		// 处理全角字符到半角
		"！", "!", "？", "?", "，", ",", "。", ".", "：", ":", "；", ";",
		"（", "(", "）", ")", "【", "[", "】", "]", "“", "\"", "”", "\"",
		"‘", "'", "’", "'", "—", "-", "…", "...",
	)
	text = strings.ToLower(text) // Convert to lowercase
	var normalized strings.Builder
	mapping := make([]int, 0, len(text))

	// Apply the replacement rules and record the positions
	for i, ch := range text {
		replaced := replacer.Replace(string(ch))
		normalized.WriteString(replaced)
		for range replaced {
			mapping = append(mapping, i)
		}
	}

	resStr := normalized.String()
	if env.GetCID() == cid2.VN {
		resStr = ToAsciiWithMappingV2(resStr)
	}
	return resStr
}

func ToAsciiWithMappingV2(text string) string {
	// 先用 NFKD 规范化，把组合音标拆分
	normalized := norm.NFKD.String(text)

	// 只保留 ASCII 字符，并转换为小写
	var result strings.Builder
	for _, r := range normalized {
		if r <= 127 { // 只保留 ASCII 码点
			result.WriteRune(unicode.ToLower(r)) // 转换为小写
		}
	}

	return result.String()
}
