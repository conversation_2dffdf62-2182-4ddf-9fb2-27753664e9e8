package util

import (
	"encoding/binary"
	"regexp"
	"strconv"
	"strings"
	"unicode"
)

func MergeMaps(map1, map2 map[string]string) map[string]string {

	// 将 map2 的键值对添加到 mergedMap 中
	for key, value := range map2 {
		if _, ok := map1[key]; !ok {
			map1[key] = value
		}
	}

	return map1
}

// TfScore 计算 R(t, F)，即查询词 term 中的所有子词在字段集合 fields 中出现的总次数
func TfScore(term string, fields []string) float64 {
	// fmt.Println("fields:", fields) // 打印字段信息，便于调试
	count := 0

	// 将查询词按空格切分为子词
	terms := strings.Fields(strings.ToLower(term)) // 转为小写后切词
	// fmt.Println("split terms:", terms)             // 打印切分后的子词

	for _, subTerm := range terms {
		for _, field := range fields {
			// 统计子词在字段中出现的次数
			if strings.EqualFold(field, subTerm) {
				count++
			}
		}
	}
	// fmt.Println("term:", term, ", count:", count) // 打印查询词和统计结果
	return float64(count)
}

// MinInt 辅助函数，返回两个整数中的最小值
func MinInt(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// MinFloat64 辅助函数，返回两 float64 中的最小值
func MinFloat64(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// MaxFloat64 辅助函数，返回两 float64 中的最大值
func MaxFloat64(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// MaxFloat64 辅助函数，返回两 float64 中的最大值
func MaxInt64(a, b int64) int64 {
	if a > b {
		return a
	}
	return b
}

// SplitToWords 将字段切分为单词数组
func SplitToWords(field string) []string {
	// 使用 FieldsFunc 按单词切分，支持保留特殊字符如连字符
	return strings.FieldsFunc(field, func(r rune) bool {
		return !unicode.IsLetter(r) && !unicode.IsDigit(r) && r != '\'' && r != '&'
	})
}

// 小写后，再将字段切分为单词数组
func LowerAndSplitToWords(field string) []string {
	field = strings.ToLower(field)

	// 使用 FieldsFunc 按单词切分，支持保留特殊字符如连字符
	return strings.FieldsFunc(field, func(r rune) bool {
		return !unicode.IsLetter(r) && !unicode.IsDigit(r) && r != '\'' && r != '&'
	})
}

// BoolToFloat 辅助函数：将布尔值转为浮点数
func BoolToFloat(b bool) float64 {
	if b {
		return 1.0
	}
	return 0.0
}

// 删除重复 item
func RemoveDuplicates(slice []string) []string {
	seen := make(map[string]struct{})
	result := []string{}
	for _, item := range slice {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

func StringToIntInterfaceList(text string) []interface{} {
	if len(text) == 0 {
		return nil
	}

	strList := strings.Split(text, ",") // 按逗号分割为字符串列表

	// 将字符串列表转换为整数列表
	intList := make([]interface{}, len(strList))
	for i, s := range strList {
		v, err := strconv.Atoi(s)
		if err != nil {
			panic("Invalid order_status configuration: " + s) // 根据需求处理错误
		}
		intList[i] = v
	}
	return intList
}

// 将关键词按指定分隔符分词并逐字分割中文
func SplitByDelimiters(input string) []string {
	// 定义正则表达式，匹配所有的分隔符
	re := regexp.MustCompile(`[,\s\-|]+`)
	// 使用正则表达式分割字符串
	words := re.Split(input, -1)

	// 存储最终结果
	var result []string

	// 遍历分割后的单词
	for _, word := range words {
		if len(word) > 0 { // 去掉空字符串
			// 如果是中文，逐字分割
			for _, r := range word {
				if unicode.Is(unicode.Han, r) {
					result = append(result, string(r))
				} else {
					// 如果不是中文，直接添加
					result = append(result, word)
					break
				}
			}
		}
	}
	return result
}

func UniqueSlice(slice []uint32) []uint32 {
	keys := make(map[uint32]struct{})
	list := make([]uint32, 0, len(slice))
	for _, entry := range slice {
		if _, exists := keys[entry]; !exists {
			keys[entry] = struct{}{}
			list = append(list, entry)
		}
	}
	return list
}

// 判断两个切片是否相等, 不考虑重复个数和顺序
func IfSliceEqual(a, b []uint32) bool {
	if len(a) == 0 && len(b) == 0 {
		return true
	}
	// 使用 map 统计元素出现的情况
	setA := make(map[uint32]struct{})
	setB := make(map[uint32]struct{})

	for _, num := range a {
		setA[num] = struct{}{}
	}
	for _, num := range b {
		setB[num] = struct{}{}
	}

	// 比较两个 map 是否一致
	if len(setA) != len(setB) {
		return false
	}

	for k := range setA {
		if _, ok := setB[k]; !ok {
			return false
		}
	}
	return true
}

func Uint32SliceToString(arr []uint32) string {
	strs := make([]string, len(arr))
	for i, num := range arr {
		strs[i] = strconv.FormatUint(uint64(num), 10)
	}
	return strings.Join(strs, ",")
}

func CountOverlap(tokens1, tokens2 []string) int {
	set := make(map[string]struct{})
	for _, t := range tokens1 {
		set[t] = struct{}{}
	}

	overlap := 0
	seen := make(map[string]struct{})
	for _, t := range tokens2 {
		if _, ok := set[t]; ok {
			if _, done := seen[t]; !done {
				overlap++
				seen[t] = struct{}{}
			}
		}
	}
	return overlap
}

// 辅助函数：将int切片转换为int32切片
func ConvertIntToInt32Slice(ints []int) []int32 {
	result := make([]int32, len(ints))
	for i, v := range ints {
		result[i] = int32(v)
	}
	return result
}

// 辅助函数：将int切片转换为int32切片
func ConvertInt32oIntSlice(ints []int32) []int {
	result := make([]int, len(ints))
	for i, v := range ints {
		result[i] = int(v)
	}
	return result
}

func IntToBytes(i int64) []byte {
	b := make([]byte, 8)
	binary.BigEndian.PutUint64(b, uint64(i))
	return b
}
