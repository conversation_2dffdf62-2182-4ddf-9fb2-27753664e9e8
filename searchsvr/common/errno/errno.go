//go:generate errnos -type ErrNo
package errno

import (
	"fmt"

	feed_errors "git.garena.com/shopee/feed/comm_lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	merrors "github.com/micro/go-micro/errors"
)

type ErrNo feed_errors.PbRPCError

func (e ErrNo) Error() string {
	return fmt.Sprintf("parser: code = %d msg = %s", e.GetCode(), e.GetMsg())
}

func (e ErrNo) GetCode() int32 {
	return e.Code
}

func (e ErrNo) GetMsg() string {
	return e.Msg
}

func As(err error) ErrNo {
	if err == nil {
		return Ok
	}

	// 处理Wrap逻辑
	var target ErrNo
	if errors.As(err, &target) {
		return target
	}

	// 处理RPC调用的业务错误
	if e, ok := err.(errors.Error); ok {
		return Reflect(uint32(e.GetCode()))
	}

	// 处理框架错误
	if e, ok := err.(*merrors.Error); ok {
		return ErrNo{
			Code: e.Code,
			Msg:  e.Error(),
		}
	}

	return ErrNo{
		Code: Unknown.Code,
		Msg:  err.Error(),
	}
}

// 这里的Msg是国际化的key值 需要i18n转成对应的Msg信息(更新msg需要重新配置i18n配置)
var (
	// [0, 999] 通用
	Ok          = ErrNo{Code: 0, Msg: "success"}
	Unknown     = ErrNo{Code: 1, Msg: "unknown"}
	ServerPanic = ErrNo{Code: 13, Msg: "server processor parser"}

	ErrParamsInvalid     = ErrNo{Code: 1000, Msg: "error_params_invalid"}
	ErrServerUnavailable = ErrNo{Code: 1004, Msg: "error_server_unavailable"}
	ErrInternalServer    = ErrNo{Code: 1008, Msg: "error_internal_server"}
	ErrQPSLimit          = ErrNo{Code: 1009, Msg: "qps limit"}

	ErrRedisUnknown     = ErrNo{Code: 1100, Msg: "redis unknown err"}
	ErrRedisNil         = ErrNo{Code: 1101, Msg: "redis: nil"}
	ErrDBUnknown        = ErrNo{Code: 1200, Msg: "db unknown err"}
	ErrESUnknown        = ErrNo{Code: 1300, Msg: "es unknown err"}
	ErrRecallUnknown    = ErrNo{Code: 1301, Msg: "recall unknown err"}
	ErrS3BucketNotFound = ErrNo{Code: 1401, Msg: "bucket not found"}
	ErrDataTypeConvert  = ErrNo{Code: 2000, Msg: "data type conversion failed"}
	ErrLimitSize        = ErrNo{Code: 3000, Msg: "exceed the limit size"}
	ErrStoreNotExisted  = ErrNo{Code: 1110023, Msg: "store not existed"}
)
