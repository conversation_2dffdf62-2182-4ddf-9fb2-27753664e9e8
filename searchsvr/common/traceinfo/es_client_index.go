package traceinfo

import (
	"context"
	"fmt"
	"math/rand"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

const (
	ESClient1 = "es-client-1"
	ESClient2 = "es-client-2"

	StoreES        = "store"
	DishES         = "dish"
	HistoryOrderES = "historyorder"
)

func buildEsClientAndIndex(ctx context.Context, traceInfo *TraceInfo) {
	defer func() {
		storeClientIndex := traceInfo.SearchESClient + traceInfo.StoreIndexIndexAlias
		dishClientIndex := traceInfo.SearchESClient + traceInfo.DishIndexIndexAlias
		historyOrderClientIndex := traceInfo.SearchESClient + traceInfo.HistoryOrderIndexIndexAlias
		logger.MyDebug(ctx, traceInfo.IsDebug, "search es client and index", zap.String("store index", storeClientIndex), zap.String("dish index", dishClientIndex), zap.String("historyOrder index", historyOrderClientIndex))
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &storeClientIndex)
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &dishClientIndex)
		_ = metric_reporter.ReportESSearchClient(1, traceInfo.HandlerType.String(), &historyOrderClientIndex)
	}()
	traceInfo.SearchESClient = selectEsClient()
	traceInfo.StoreIndexIndexAlias = selectEsIndexAlias(traceInfo, StoreES, apollo.SearchApolloCfg.ESSearchTrafficSplit.StoreTrafficConfig)
	traceInfo.DishIndexIndexAlias = selectEsIndexAlias(traceInfo, DishES, apollo.SearchApolloCfg.ESSearchTrafficSplit.DishTrafficConfig)
	traceInfo.HistoryOrderIndexIndexAlias = selectEsIndexAlias(traceInfo, HistoryOrderES, apollo.SearchApolloCfg.ESSearchTrafficSplit.HistoryOrderTrafficConfig)
}

func selectEsClient() string {
	t1 := int(apollo.SearchApolloCfg.ESSearchTrafficSplit.TrafficClient1)
	t2 := int(apollo.SearchApolloCfg.ESSearchTrafficSplit.TrafficClient2)
	sum := t1 + t2
	// 未配置Apollo，直接跳过
	if t1 == 0 && t2 == 0 {
		return ESClient1
	}
	// 不走client2,全部走client1
	if t2 == 0 {
		return ESClient1
	}
	// 不走client1,全部走client2
	if t1 == 0 {
		return ESClient2
	}
	r := rand.Intn(sum) // 0 ~ sum-1
	if r >= t1 {
		return ESClient2
	}
	return ESClient1
}

func IsDishUseRouting(traceInfo *TraceInfo, indexType string) bool {
	if indexType == DishES && (traceInfo.HandlerType == HandlerTypeSearchStoresWithListingDish || traceInfo.HandlerType == HandlerTypeSearchCollectionWithListingDish || traceInfo.HandlerType == HandlerTypeSearchDishesRecall) {
		if env.GetEnv() == "live" || env.GetEnv() == "liveish" {
			return true
		}
	}
	return false
}

func selectEsIndexAlias(traceInfo *TraceInfo, indexType string, indexTrafficConfig apollo.IndexTrafficConfig) string {
	if indexType == HistoryOrderES {
		indexName := fmt.Sprintf("foodalgo_dataplatform_%v_%v_store_search_index_history", env.GetEnv(), env.GetCID())
		return indexName
	}
	indexName := fmt.Sprintf("foodalgo_new_%v_%v_%s_search_index", env.GetEnv(), env.GetCID(), indexType)
	if IsDishUseRouting(traceInfo, indexType) {
		indexName = fmt.Sprintf("foodalgo_dataplatform_%v_%v_dish_search_index_routing", env.GetEnv(), env.GetCID())
	}
	return indexName
}

func GetESClientFromPool(traceInfo *TraceInfo, indexType string) *es.ESBase {
	indexAlias := traceInfo.StoreIndexIndexAlias
	if indexType == DishES {
		indexAlias = traceInfo.DishIndexIndexAlias
	}
	if indexType == HistoryOrderES {
		indexAlias = traceInfo.HistoryOrderIndexIndexAlias
	}
	clientAndIndex := traceInfo.SearchESClient + indexAlias

	if es.ESClientPool[clientAndIndex] == nil {
		es.ESClientPoolLock.Lock()
		if es.ESClientPool[clientAndIndex] == nil {
			esClient := es.ESClient1
			if traceInfo.SearchESClient == ESClient2 {
				esClient = es.ESClient2
			}
			eSBase := es.NewES(indexAlias, esClient)
			es.ESClientPool[clientAndIndex] = eSBase
		}
		es.ESClientPoolLock.Unlock()
	}
	return es.ESClientPool[clientAndIndex]
}
