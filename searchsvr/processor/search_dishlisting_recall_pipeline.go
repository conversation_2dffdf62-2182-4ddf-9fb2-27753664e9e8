package processor

import (
	"context"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"github.com/golang/protobuf/proto"
	"strings"
)

func SearchDishListingPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo, storeIds []uint64) ([]*foodalgo_search.DishRecallItem, error) {
	// 每个pipeline进来要初始化数据源
	preprocess.TraceInfoReInitDataSource(ctx, traceInfo)

	// 门店挂菜
	storeInfos := make(model.StoreInfos, 0, len(storeIds))
	for _, storeId := range storeIds {
		storeInfo := &model.StoreInfo{
			StoreId: storeId,
		}
		storeInfos = append(storeInfos, storeInfo)
	}
	storeInfos = RecallListingDishesWithoutCache(ctx, traceInfo, storeInfos)
	if traceInfo.IsDebug {
		debugInfo.FillNormalStores(traceInfo, storeInfos)
		debugInfo.FillMixStores(traceInfo, storeInfos)
	}

	rspItems := make([]*foodalgo_search.DishRecallItem, 0, len(storeInfos))
	for _, store := range storeInfos {
		dishes := make([]*foodalgo_search.DishItem, 0, len(store.DishInfos))
		for _, dish := range store.DishInfos {
			dishes = append(dishes, &foodalgo_search.DishItem{
				DishId:        proto.Uint64(dish.DishId),
				StoreId:       proto.Uint64(dish.StoreId),
				DishName:      proto.String(dish.DishName),
				Score:         proto.Float32(float32(dish.Score)),
				Available:     proto.Bool(dish.Available == foodalgo_search.Available_AVAILABLE),
				ListingStatus: proto.Bool(dish.ListingStatus == foodalgo_search.DishListingStatus_ACTIVE),
				HasPicture:    proto.Bool(dish.HasPicture),
				SalesVolume:   proto.Int32(int32(dish.SalesVolume)),
				Price:         proto.Uint64(dish.Price),
				CreateTime:    proto.Uint64(dish.CreateTime),
				Picture:       proto.String(dish.Picture),
				IsOnSale:      proto.Bool(dish.SaleStatus == o2oalgo.DishSale_DISH_SALE_FOR_SALE),
				RecallTypes:   proto.String(strings.Join(dish.DishRecallTypes, "|")),
			})
		}
		rspItems = append(rspItems, &foodalgo_search.DishRecallItem{
			StoreId:   proto.Uint64(store.StoreId),
			DishItems: dishes,
		})
	}

	return rspItems, nil
}
