package filling

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_real_name"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_datamanagement"
	"go.uber.org/zap"
)

func StoreMetaFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, esStores model.StoreInfos) model.StoreInfos {
	storeMetaSize := 0
	pt := time.Now()
	downgradeErrCode := "0"
	defer func() {
		decision.InitDataServerDowngradeWithReturnSize(ctx, traceInfo, len(esStores), storeMetaSize) // 根据正排返回结果数判断是否需要降级, 并更新到 traceInfo.IsDowngradeDataServer
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreMeta, time.Since(pt))
		reporter.ReportClientRequestError(1, "data_server_downgrade", downgradeErrCode)
	}()

	decision.InitDataServerDowngrade(traceInfo) // 正排降级，并且赋值给 traceInfo.IsDowngradeDataServer
	if traceInfo.IsDowngradeDataServer {
		logger.MyDebug(ctx, traceInfo.IsDebug, "data server downgrade, skip store info filling")
		downgradeErrCode = "true"
		return esStores
	}

	storeMetaMap := batchGetStoreMeta(ctx, traceInfo, esStores.DistinctStoreIDs())
	lng := traceInfo.TraceRequest.Longitude
	lat := traceInfo.TraceRequest.Latitude
	storeInfos := make([]*model.StoreInfo, 0, len(esStores))
	keywordNor := util2.StringNormalize(traceInfo.QueryKeyword) // query 归一化处理,去越南语调(vn)，大写转小写，去特殊符号且多个空格保留一个
	for _, storeInfo := range esStores {
		storeMeta, exist := storeMetaMap[storeInfo.StoreId]
		if exist == false || storeMeta == nil || storeMeta.GetId() == 0 {
			traceInfo.AddFilteredStore(storeInfo.StoreId, traceinfo.FilteredTypeWithoutStoreMeta, storeInfo)
			continue
		}
		storeMetaSize++
		fillingWithMeta(ctx, storeInfo, storeMeta) // 正排字段填充
		// 特殊处理
		fillingRealAndBranchName(storeInfo)
		fillingCategory(ctx, storeInfo)
		fillingRecallSuppressRate(ctx, storeInfo)
		storeInfo.FlyingDistance = storeInfo.CountDistance(traceInfo.HandlerType, lng, lat)
		storeInfo.Distance = storeInfo.FlyingDistance
		storeInfo.DistanceScore = storeInfo.CountDistanceScore(traceInfo.HandlerType)
		storeInfo.DistanceGroup5km = storeInfo.CountDistanceGroup5km()
		storeInfo.RatingTotalGroup5 = storeInfo.CountRatingTotalGroup5()
		storeInfo.TermMatchScore = util2.CountTermMatchScore(keywordNor, util2.StringNormalize(storeInfo.StoreName))
		storeInfos = append(storeInfos, storeInfo)
	}

	return storeInfos
}

func fillingIsPreciseAndExactMatch(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos []*model.StoreInfo, keywordNor string) {
	isNeedExactMatch := abtest.GetIsHitExactMatch(traceInfo.IsDebug, traceInfo.AbParamClient)
	if isNeedExactMatch == false {
		return
	}
	if cid.IsVN() == false {
		// ID/TH/MY
		for _, storeInfo := range storeInfos {
			storeInfo.IsPreciseStore = isStoreOrRealName(storeInfo.StoreName, storeInfo.RealName, keywordNor) // IsPreciseStore 使用store name == query和real name == query
			storeInfo.ExactMatch = storeInfo.IsPreciseStore                                                   // ID/TH/MY的exact match 使用store name == query和real name == query, 和IsPreciseStore 一样
		}
		return
	} else {
		// VN
		hasAccent := !util2.IsAsciiTerms(traceInfo.QueryKeyword)
		for _, storeInfo := range storeInfos {
			storeInfo.IsPreciseStore = isStoreOrRealName(storeInfo.StoreName, storeInfo.RealName, keywordNor) // VN IsPreciseStore 使用store name == query和real name == query
			storeInfo.ExactMatch = vnExactMatch(storeInfo.StoreName, traceInfo.QueryKeyword, hasAccent)       // VN Exact Match 处理有几个分档
		}
	}
}

func isStoreOrRealName(storeName, realName, keywordNor string) uint32 {
	if len(storeName) == 0 || len(keywordNor) == 0 {
		return 0
	}
	if keywordNor == util2.StringNormalize(storeName) {
		return 1
	}
	if keywordNor == util2.StringNormalize(realName) {
		return 1
	}
	return 0
}

// vn exact match  https://confluence.shopee.io/pages/viewpage.action?pageId=2197466387
func vnExactMatch(storeName, query string, hasAccent bool) uint32 {
	if len(query) == 0 || len(storeName) == 0 {
		return 0
	}
	if hasAccent {
		queryLower := strings.TrimSpace(strings.ToLower(util2.RemoveExtraSpace(query)))
		storeNameLower := strings.TrimSpace(strings.ToLower(util2.RemoveExtraSpace(storeName)))

		// 第一档,完全一样
		if queryLower == storeNameLower {
			return 4
		}

		queryRemove := replaceAndTrim(queryLower, "-", "")
		storeNameRemove := replaceAndTrim(storeNameLower, "-", "")

		// 第二档, 去除- 完全一样
		if queryRemove == storeNameRemove {
			return 3
		}

		queryToAscii := util2.ToAscii(queryLower)
		storeNameToAscii := util2.ToAscii(storeNameLower)

		// 第三档, 去除音调 完成一样
		if queryToAscii == storeNameToAscii {
			return 2
		}

		queryToAsciiRemove := util2.ToAscii(queryRemove)
		storeNameToAsciiRemove := util2.ToAscii(storeNameRemove)

		//第四档, 去除音调 去除- 完全一样
		if queryToAsciiRemove == storeNameToAsciiRemove {
			return 1
		}
	} else {
		queryToAscii := util2.ToAscii(strings.TrimSpace(util2.RemoveExtraSpace(strings.ToLower(query))))
		storeNameToAscii := util2.ToAscii(strings.TrimSpace(util2.RemoveExtraSpace(strings.ToLower(storeName))))
		// 不带声调, 第一档 去除声调后一样
		if queryToAscii == storeNameToAscii {
			return 2
		}

		queryToAsciiRemove := util2.ToAscii(replaceAndTrim(query, "-", ""))
		storeNameToAsciiRemove := util2.ToAscii(replaceAndTrim(storeName, "-", ""))
		// 不带声调, 第二档 去除声调 去除-
		if queryToAsciiRemove == storeNameToAsciiRemove {
			return 1
		}
	}

	return 0
}

func replaceAndTrim(word, replaceOldStr, replaceNewStr string) string {
	newWord := strings.ReplaceAll(word, replaceOldStr, replaceNewStr)
	newWord = strings.TrimSpace(util2.RemoveExtraSpace(newWord))
	return newWord
}

func batchGetStoreMeta(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) map[uint64]*o2oalgo.Store {
	nStore := len(storeIds)
	if nStore == 0 {
		return map[uint64]*o2oalgo.Store{}
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreMetaRPC, time.Since(pt))
	}()
	timeOut := 200
	if apollo.SearchApolloCfg.ClientTimeOutConfig.DataManageTimeOut > 0 {
		timeOut = apollo.SearchApolloCfg.ClientTimeOutConfig.DataManageTimeOut
	}
	fromInfo := &o2oalgo_datamanagement.FromInfo{
		Scene: traceInfo.HandlerType.String(),
	}
	t1 := time.Now()
	rsp := integrate.DataManageServiceClient.GetStoreMetaV3(ctx, storeIds, traceInfo.TraceRequest.SearchTime, timeOut, nil, fromInfo)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetStoreMetaV3"), zap.String("t1", time.Since(t1).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(t1), metric_reporter2.SearchReportTypeRpc, "", "", "GetStoreMetaV3")
	if rsp.ErrCount > 0 {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_META_ERROR)
	}
	for filterType, reasons := range rsp.GetFilterReasons() {
		for _, Id := range reasons.GetIds() {
			traceInfo.AddFilteredStore(Id, filterType, nil)
		}
	}
	if rsp.GetMapStore() != nil {
		return rsp.GetMapStore()
	}
	return map[uint64]*o2oalgo.Store{}
}
func fillingCategory(ctx context.Context, storeInfo *model.StoreInfo) {
	storeInfo.MainCategory = model.GetMainCategory(ctx, storeInfo.StoreId, storeInfo.MainCategoryIdStr)
	storeInfo.SubCategory = model.GetSubCategory(ctx, storeInfo.StoreId, storeInfo.SubCategoryIdStr)
	storeInfo.MainCategoryNameStr = model.GetMainCategoryNameStr(storeInfo.MainCategory)
	storeInfo.SubCategoryNameStr = model.GetSubCategoryNameStr(storeInfo.SubCategory)
	if env.GetCID() != cid.VN {
		storeInfo.L2CategoryId = model.GetL2CategoryId(storeInfo.MainCategory, storeInfo.SubCategory)
		storeInfo.L1CategoryId = model.GetL1CategoryId(storeInfo.MainCategory, storeInfo.SubCategory)
	}
}

func fillingRealAndBranchName(storeInfo *model.StoreInfo) {
	if store_real_name.StoreRealNameDict == nil {
		return
	}
	if cid.IsVN() {
		storeInfo.StoreNameAscii = util2.ToAscii(storeInfo.StoreName)
	}
	// real name & branch name
	storeInfo.RealName = storeInfo.StoreName
	storeInfo.BranchName = ""
	storeRealNameBytes, err := store_real_name.StoreRealNameDict.Dict.Get(util2.IntToBytes(int64(storeInfo.StoreId)))
	if err == nil && len(storeRealNameBytes) > 0 {
		splits := strings.Split(string(storeRealNameBytes), store_real_name.Separator)
		if len(splits) == 3 && splits[0] == storeInfo.StoreName {
			storeInfo.RealName = splits[1]
			storeInfo.BranchName = splits[2]
		}
	}
}

func fillingRecallSuppressRate(ctx context.Context, storeInfo *model.StoreInfo) {
	// RecallSuppressType  0-不进行打压，1-当门店仅从此路召回时,打压系数为1.0，2-当门店有从此路召回时打压系数为1.0
	switch storeInfo.RecallSuppressType {
	case 1:
		if len(storeInfo.RecallTypes) == 1 {
			storeInfo.IsRecallSuppress = 1
		}
	case 2:
		storeInfo.IsRecallSuppress = 1
	}
}

func fillingWithMeta(ctx context.Context, storeInfo *model.StoreInfo, store *o2oalgo.Store) {
	if storeInfo == nil {
		logkit.FromContext(ctx).Error("storeInfo is nil, skip")
		return
	}
	if store == nil {
		logkit.FromContext(ctx).Error("store is nil", zap.Uint64("store id", storeInfo.StoreId), zap.Any("store", store))
		return
	}
	storeInfo.StoreId = store.GetId()
	storeInfo.StoreName = store.GetName()
	storeInfo.BrandId = store.GetBrandId()
	storeInfo.Location = store.GetLocation()
	storeInfo.RatingTotal = store.GetRatingTotal()
	storeInfo.RatingScore = float64(store.GetRatingScore())
	if cid.IsVN() {
		storeInfo.RatingScore = storeInfo.RatingScore / 100.0 // ID 存储的是4.19， VN 存储的是419
	}
	storeInfo.StoreSellWeek = store.GetSellWeek()
	storeInfo.DisplayOpeningStatus = store.GetDisplayOpeningStatus()
	storeInfo.DisplayDistrictStatus = store.GetDisplayDistrictStatus()
	storeInfo.Logo = store.GetLogo()
	storeInfo.MainCategoryIdStr = store.GetMainCategory()
	storeInfo.SubCategoryIdStr = store.GetSubCategory()
	storeInfo.DeliveryDistance = store.GetDeliveryDistance()
	storeInfo.OriginalDeliveryDistance = store.GetOriginalDeliveryDistance()
	storeInfo.SelfPickupDistance = store.GetSelfPickupDistance()
	storeInfo.Timezone = store.GetTimezone()
	storeInfo.MerchantId = store.GetMerchantId()
	storeInfo.HalalType = foodalgo_search.HalalType(store.GetHalalType())
	storeInfo.CreateTime = store.GetCreateTimeMs()
	storeInfo.PartnerType = store.GetPartnerType()
	storeInfo.StoreStatus = store.GetStatus()
	storeInfo.IsPreferredMerchant = store.GetIsPreferredMerchant()
	storeInfo.IsSelfPickUp = store.GetIfSupportSelfPickup()
	storeInfo.IsFoodyDelivery = store.GetIsFoodyDelivery()
	storeInfo.SearchNonHalalFlag = store.GetSearchNonHalalFlag()
	storeInfo.City = store.GetLocation().GetCity()
	storeInfo.District = store.GetLocation().GetDistrict()
	storeInfo.StoreAvailableDishCnt = int64(store.GetDishSalesNum()) // GetStoreMetaV3 不再单独获取
	storeInfo.StoreDishCnt = int64(store.GetDishNum())               // GetStoreMetaV3 不再单独获取
	storeInfo.CategoryType = store.GetCategoryType()

	// 线上partner type数据只有1-listed, 2-normal
	// partner type 0,1  => IsPartnerMerchant=false
	// partner type 2,3,4,5 => IsPartnerMerchant=true, 且vn没有5类型
	storeInfo.IsPartnerMerchant = 1
	if store.GetPartnerType() == o2oalgo.PartnerType_PARTNER_TYPE_UNKNOWN || store.GetPartnerType() == o2oalgo.PartnerType_PARTNER_TYPE_LISTED {
		storeInfo.IsPartnerMerchant = 0
	}
	storeInfo.StoreTags = make(map[uint64]struct{})
	for i := range store.GetTagIds() {
		storeInfo.StoreTags[store.GetTagIds()[i]] = struct{}{}
	}
}
