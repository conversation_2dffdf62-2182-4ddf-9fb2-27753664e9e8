package localcache

import (
	"fmt"
	"runtime"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"github.com/coocood/freecache"
)

var routingDistanceNilDataExpireSec = 10
var routingDistanceDataExpireSec = 60 * 60
var storePolygonCacheExpireSec = 60 * 60 * 6
var nextCacheTime = 0

func GetNextCacheTimeSec(cache int) int {
	nextCacheTime += 1
	if nextCacheTime > 120 {
		nextCacheTime = 0
	}
	return nextCacheTime + cache
}

var StoreCacheSysInstance *StoreCacheSys

type StoreCacheSys struct {
	polygonCache          *freecache.Cache
	dishMetaCache         *freecache.Cache
	dishFeatureCache      *freecache.Cache
	flashSaleCache        *freecache.Cache
	dishFromFSRecallCache *freecache.Cache
}

func InitStoreCacheSys() {
	dishMetaCacheSize := apollo.SearchApolloCfg.DishFreeCacheSize
	if dishMetaCacheSize == 0 {
		dishMetaCacheSize = ********** // 2GB
	}
	StoreCacheSysInstance = &StoreCacheSys{
		polygonCache:          InitFreeCache("polygonCache", **********),          // 2GB
		dishMetaCache:         InitFreeCache("dishMetaCache", dishMetaCacheSize),  // 配置获取
		dishFeatureCache:      InitFreeCache("dishFeatureCache", **********),      // 1G
		flashSaleCache:        InitFreeCache("flashSaleCache", **********),        // 1G
		dishFromFSRecallCache: InitFreeCache("dishFromFSRecallCache", **********), // 2GB
	}
}

func InitFreeCache(catchName string, cacheSize int) *freecache.Cache {
	cache := freecache.NewCache(cacheSize)

	go func() {
		for {
			time.Sleep(time.Duration(15) * time.Second)
			ReportForFreeCache(cache, catchName)
			//PrintGCInfo()
		}
	}()
	return cache
}

func ReportForFreeCache(c *freecache.Cache, name string) {
	if c == nil {
		return
	}

	totalEvacuate := c.EvacuateCount() // 淘汰次数
	totalExpired := c.ExpiredCount()   // 超时次数
	overwrites := c.OverwriteCount()   // 覆盖次数
	hitCount := c.HitCount()           // 命中次数
	missCount := c.MissCount()         // 丢失次数
	lookupCount := c.LookupCount()     // 命中 + 丢失
	hitRate := c.HitRate()             // 命中 / (命中 + 丢失)
	entryCount := c.EntryCount()       // 总数
	c.ResetStatistics()
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "totalEvacuate", float64(totalEvacuate))
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "totalExpired", float64(totalExpired))
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "overwrites", float64(overwrites))
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "hitCount", float64(hitCount))
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "missCount", float64(missCount))
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "lookupCount", float64(lookupCount))
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "hitRate", hitRate)
	metric_reporter2.ReportSearchFreeCacheStatistics(name, "entryCount", float64(entryCount))

	//fmt.Printf("[FreeCache Stats - %s @ %s]\n", name, time.Now().Format("15:04:05"))
	//fmt.Printf("[FreeCache Stats -  [%s] EntryCount:     %d\n", name, entryCount)
	//fmt.Printf("[FreeCache Stats -  [%s] EvacuateCount:  %d\n", name, totalEvacuate)
	//fmt.Printf("[FreeCache Stats -  [%s] ExpiredCount:   %d\n", name, totalExpired)
	//fmt.Printf("[FreeCache Stats -  [%s] OverwriteCount: %d\n", name, overwrites)
	//fmt.Printf("[FreeCache Stats -  [%s] HitCount:       %d\n", name, hitCount)
	//fmt.Printf("[FreeCache Stats -  [%s] MissCount:      %d\n", name, missCount)
	//fmt.Printf("[FreeCache Stats -  [%s] LookupCount:    %d\n", name, lookupCount)
	//fmt.Printf("[FreeCache Stats -  [%s] HitRate:        %.2f\n", name, hitRate)
	//fmt.Println("--------------------------------------------------")
}

func PrintGCInfo() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	fmt.Printf("GC Info 次数: %v\n", m.NumGC)
	fmt.Printf("GC Info 总耗时: %v\n", time.Duration(m.PauseTotalNs))
}
