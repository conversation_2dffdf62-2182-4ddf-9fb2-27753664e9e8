package search_limiter

import (
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"golang.org/x/time/rate"
)

// 默认限流配置
const (
	defaultQPS   = 1000 // 默认每秒1000个请求
	defaultBurst = 100  // 默认突发容量100
)

// 方法级别的限流配置
var methodConfigs = map[string]struct {
	qps   int
	burst int
}{
	"SearchDishRecall": {qps: 500, burst: 50},
	// 可以为其他方法添加配置
	// "Search": {qps: 2000, burst: 200},
}

type RateLimiterService struct {
	limiters map[string]*rate.Limiter
	mutex    sync.RWMutex
	disabled bool // 标识是否处于降级模式
}

func (r *RateLimiterService) Allow(ctx context.Context, method string) bool {
	// 降级模式下允许所有请求通过
	if r == nil {
		logkit.FromContext(ctx).Error("rate limiter is nil, allowing all requests", logkit.String("method", method))
		return true
	}

	rateLimiter := limiter.GetRateLimiter(constant.Method, method)
	if rateLimiter != nil {
		if !rateLimiter.Allow() {
			metric_reporter.ReportCounter(metric_reporter.ReportServiceRateLimitRequestMetricName, 1, reporter.Label{
				Key: "method",
				Val: method,
			})
			logkit.Error("limiter reject request", logkit.String("method", method))
			return false
		} else {
			return true
		}
	} else {
		// 未配置限流器时允许请求通过，避免因配置问题导致服务不可用
		logkit.Warn("no rate limiter configured for method, allowing request", logkit.String("method", method))
		return true
	}
}

func InitRateLimitConfig(source string) *RateLimiterService {
	gatewayModuleCurrentSecret := gatewayModuleNonLiveSecret
	if env.Environment() == "live" || env.Environment() == "liveish" {
		gatewayModuleCurrentSecret = gatewayModuleLiveSecret
	}
	if err := register.InitRateLimitConfig(group, project, gatewayModule, gatewayModuleCurrentSecret, env.Region(), source); err != nil {
		logkit.Error("failed to init rate limit, falling back to disabled mode", logkit.Err(err))
		// 返回降级模式的限流器，而不是panic
		return &RateLimiterService{}
	}
	logkit.Info("rate limiter initialized successfully")
	return &RateLimiterService{}
}
