ifeq (${env},)
   export env=dev
endif

ifeq (${cid},)
   export cid=local
endif

ENV=${env}
REGION=${cid}

PROJECT=foodalgo
SHELL=/bin/bash
# SERVICE=$(word $(words $(subst /, ,${service})), $(subst /, ,${service}))
SERVICE=$(word 1, $(subst /, ,${service}))
TARGET=$(PROJECT)-$(SERVICE)
PROJECT_ROOT= $(patsubst %/,%,$(abspath $(dir $$PWD)))
SERVICE_ROOT=$(PROJECT_ROOT)/$(service)

# svr适用
ALL_YMLS = ${wildcard $(SERVICE_ROOT)/conf/*conf.yml}
FILE_YMLS = $(notdir  $(ALL_YMLS))
MODULENAME=search
CONFIG_SRC=$(SERVICE_ROOT)/conf/${MODULENAME}_$(ENV)_$(REGION)_conf.yml
ifeq (${cid},local)
    CONFIG_SRC=$(SERVICE_ROOT)/conf/${MODULENAME}_$(REGION)_conf.yml
endif

# api适用
ISAPI=$(shell if [[ ${service} == *"api" ]]; then echo "api"; fi)
$(info $$ISAPI = ${ISAPI})
ifeq (${ISAPI},api)
	CONFIG_SRC=$(SERVICE_ROOT)/conf/config_$(ENV)_$(REGION).yml
endif

CONFIG=$(SERVICE_ROOT)/conf/config.yml

SRC=$(shell find $(SERVICE_ROOT) -maxdepth 1 -type f \( -name "*.go" ! -name "*_test.go" \))

ssh -vvvCNL ~/run/spex/spex.sock:/run/spex/spex.sock -o StreamLocalBindUnlink=yes shopee-spex-test-server &

GO=go
GOFLAGS=
GOMAJORVERSION=$(shell go version | cut -d" " -f 3 | cut -d"." -f 1)
GOSUBVERSION=$(shell go version | cut -d" " -f 3 | cut -d"." -f 2)
GORACE=-race

ifeq (${env},dev)
GOBUILDFLAGS=${GORACE}
endif

# ifeq (${env},test)
# GOBUILDFLAGS=${GORACE}
# endif

export GO111MODULE=on

$(info $$GOVERSION = "${GOMAJORVERSION}.${GOSUBVERSION}")
$(info $$GOPATH = "${GOPATH}")
$(info $$PATH = "${PATH}")
$(info $$ENV = "${ENV}")
$(info $$REGION = "${REGION}"$N)
$(info $$TARGET = "${TARGET}")
$(info $$PROJECT_ROOT = "${PROJECT_ROOT}")
$(info $$SERVICE_ROOT = "${SERVICE_ROOT}")
$(info $$CONFIG_SRC = "${CONFIG_SRC}")
$(info $$CONFIG = "${CONFIG}"$N)
$(info $$SRC = "$(foreach one,$(SRC),$(one:$(SERVICE_ROOT)//%=%))")

$(CONFIG): $(CONFIG_SRC)
	ln -sf $< $@

$(TARGET):
	cd $(PROJECT_ROOT)
	$(GO) build -mod=vendor  -ldflags="-checklinkname=0" -pgo=./pgo_files/profile.pgo -tags=jsoniter ${GOBUILDFLAGS} -o $(PROJECT_ROOT)/$(service)/$@ $(SRC) $(GOFLAGS) || exit 1;

build: $(CONFIG) $(TARGET)

clean:
	find . -type f                  -name "*.log*"       -exec rm {}     + # 清理日志
	find . -type d -empty           -name "log"          -exec rm -rf {} + # 清理空日志文件夹
	find . -type f -perm 0755       -name "foodalgo-*"      -exec rm {}     + # 清理 foodalgo 的可执行文件
	find . -type l -path "*/conf/*" -name "*config.y*ml" -exec rm {}     + # 清理配置文件软链

start: $(CONFIG)  $(LOGCONFIG)
	mkdir -p ~/run/spex; \
	chmod 777 ~/run/spex; \
	touch ~/run/spex/spex.sock; \
	ssh -vvvCNL ~/run/spex/spex.sock:/run/spex/spex.sock -o StreamLocalBindUnlink=yes shopee-spex-test-server & \
	export SP_UNIX_SOCKET=~/run/spex/spex.sock; \
	$(GO) run -mod=vendor $(SRC) -config=$(CONFIG); \

dep:
	$(GO) mod tidy
	$(GO) mod vendor

build-test:
	for SERVICE in `find . -type d -d 1 -name '*svr' -or -type d -d 1 -name '*api'`; do \
		echo "building $$SERVICE..."; \
		pushd $$SERVICE > /dev/null; \
		SERVICENAME=`basename $$SERVICE`; \
		go build -ldflags="-checklinkname=0" -pgo=./pgo_files/profile.pgo -mod=vendor ${GOBUILDFLAGS} -o  $(PROJECT)-$$SERVICENAME  || (echo "build $$SERVICE failed"); \
		moduleName=`find ./conf -name '*.yml' | awk 'NR==1{print}' | awk -F '/' '{print $$3}' | awk -F '_' '{print $$1}'`;\
		confSRC1=${PROJECT_ROOT}/$$SERVICENAME/conf/$${moduleName}_$(ENV)_$(REGION)_conf.yml; \
		confSRC2=${PROJECT_ROOT}/$$SERVICENAME/conf/$${moduleName}_$(REGION)_conf.yml; \
		confSRC=`if [ ${REGION} == "local" ];then echo $${confSRC2};else echo $${confSRC1}; fi`; \
		ln -sf $$confSRC ${PROJECT_ROOT}/$$SERVICENAME/conf/config.yml; \
		popd > /dev/null; \
	done

all: build-test
