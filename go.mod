module git.garena.com/shopee/toc/foodalgo/service

go 1.23

require (
	git.garena.com/shopee/common/ulog v0.2.4 // indirect
	git.garena.com/shopee/digital-purchase/common v1.1.37-0.20220113081053-9d746a31ff58
	git.garena.com/shopee/digital-purchase/recsys/common v1.0.6
	git.garena.com/shopee/experiment-platform/abtest-core/v2 v2.4.19
	git.garena.com/shopee/feed/comm_lib v1.2.6
	git.garena.com/shopee/feed/ginweb v1.1.9
	git.garena.com/shopee/feed/microkit v1.2.8
	git.garena.com/shopee/foody/service v1.56.1-061001-hotfix-delivery-panic.0.20240411065120-1898b89eac02
	git.garena.com/shopee/game_platform/go-authsdk v1.1.0
	git.garena.com/shopee/golang_splib v0.3.5 // indirect
	git.garena.com/shopee/marketing/config-client v1.3.2
	git.garena.com/shopee/o2o-intelligence/common/common-lib v0.0.0-20250708091823-bd7ffd318540
	git.garena.com/shopee/o2o-intelligence/ml-common/public-message v1.0.3-2025022601.0.20250521125322-6406f50e1c75
	git.garena.com/shopee/platform/golang_splib v1.2.7
	git.garena.com/shopee/platform/service-governance/observability/metric v1.0.20
	git.garena.com/shopee/sp_protocol v1.3.33
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible
	github.com/bytedance/sonic v1.13.3-0.20250326071816-1b6ac947a802 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/coocood/freecache v1.2.2
	github.com/go-sql-driver/mysql v1.7.0
	github.com/gogo/protobuf v1.3.2
	github.com/golang/protobuf v1.5.4
	github.com/google/wire v0.6.0
	github.com/hkulekci/ascii-folding v0.0.0-20170317205928-8d72abd50aaf
	github.com/jinzhu/copier v0.3.5
	github.com/json-iterator/go v1.1.12
	github.com/micro/go-micro v1.18.0
	github.com/olivere/elastic/v7 v7.0.27
	github.com/robfig/cron v1.2.0
	github.com/samuel/go-zookeeper v0.0.0-20201211165307-7117e9ea2414
	github.com/satori/go.uuid v1.2.0
	github.com/stretchr/testify v1.10.0
	github.com/urfave/cli/v2 v2.3.0
	go.uber.org/zap v1.26.0
	golang.org/x/sync v0.9.0 // indirect
	golang.org/x/text v0.20.0
	golang.org/x/time v0.5.0
	google.golang.org/grpc v1.62.1 // indirect
	google.golang.org/protobuf v1.36.5
	gopkg.in/yaml.v2 v2.4.0
)

require (
	git.garena.com/common/go-common-tool v0.1.1 // indirect
	git.garena.com/common/gocommon v0.0.0-20210823105047-9537185f8398 // indirect
	git.garena.com/common/gommon/crypt v0.0.0-20210211075301-867bb6bc3c33 // indirect
	git.garena.com/shopee/common/jsonext v0.1.0 // indirect
	git.garena.com/shopee/common/observability_config v0.2.1 // indirect
	git.garena.com/shopee/devops/golang_aegislib v0.0.13 // indirect
	git.garena.com/shopee/experiment-platform/abtest-config-service v1.1.2-sdk // indirect
	git.garena.com/shopee/experiment-platform/abtest-model v0.3.6 // indirect
	git.garena.com/shopee/experiment-platform/gateway-sdk-go v1.2.2 // indirect
	git.garena.com/shopee/platform/config-sdk-go v0.10.0-compat // indirect
	git.garena.com/shopee/platform/config-sdk-go/adapter/market-config-sdk v0.3.2 // indirect
	git.garena.com/shopee/platform/jaeger-tracer v1.9.2 // indirect
	git.garena.com/shopee/platform/service-governance/api/grpc_middleware v1.0.5 // indirect
	git.garena.com/shopee/platform/service-governance/viewercontext v1.0.13 // indirect
	git.garena.com/shopee/platform/splog v1.5.2 // indirect
	git.garena.com/shopee/platform/trace v0.1.0 // indirect
	git.garena.com/shopee/platform/tracing-contrib/dynamic-sampler v0.1.1 // indirect
	github.com/BurntSushi/toml v0.4.1 // indirect
	github.com/adamzy/cedar-go v0.0.0-20170805034717-80a9c64b256d // indirect
	github.com/aws/aws-sdk-go v1.52.5 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bitly/go-simplejson v0.5.0 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cloudwego/hertz v0.9.0 // indirect
	github.com/coreos/etcd v3.3.17+incompatible // indirect
	github.com/coreos/go-systemd v0.0.0-20191104093116-d3cd4ed1dbcf // indirect
	github.com/coreos/pkg v0.0.0-20180928190104-399ea9e2e55f // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.0-20190314233015-f79a8a8ca69d // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/fsnotify/fsnotify v1.5.4 // indirect
	github.com/gabriel-vasile/mimetype v1.4.7 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.10.0 // indirect
	github.com/go-log/log v0.1.0 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.20.0 // indirect
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/google/pprof v0.0.0-20211214055906-6f57359322fd // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gopherjs/gopherjs v0.0.0-20181017120253-0766667cb4d1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/huichen/sego v0.0.0-20210824061530-c87651ea5c76 // indirect
	github.com/imdario/mergo v0.3.8 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/jmoiron/sqlx v1.2.1-0.20190826204134-d7d95172beb5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/liuzl/cedar-go v0.0.0-20170805034717-80a9c64b256d // indirect
	github.com/liuzl/da v0.0.0-20180704015230-14771aad5b1d // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/micro/cli v0.2.0 // indirect
	github.com/micro/mdns v0.3.0 // indirect
	github.com/miekg/dns v1.1.41 // indirect
	github.com/mitchellh/hashstructure v1.0.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/mozillazg/go-unidecode v0.2.0 // indirect
	github.com/nats-io/jwt v1.1.0 // indirect
	github.com/nats-io/nats.go v1.10.0 // indirect
	github.com/nats-io/nkeys v0.1.4 // indirect
	github.com/nats-io/nuid v1.0.1 // indirect
	github.com/onsi/ginkgo v1.16.5 // indirect
	github.com/onsi/ginkgo/v2 v2.11.0 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/panjf2000/ants/v2 v2.8.2 // indirect
	github.com/parnurzeal/gorequest v0.2.16 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.19.1 // indirect
	github.com/prometheus/client_model v0.5.0 // indirect
	github.com/prometheus/common v0.48.0 // indirect
	github.com/prometheus/procfs v0.12.0 // indirect
	github.com/quic-go/quic-go v0.50.0 // indirect
	github.com/russross/blackfriday/v2 v2.0.1 // indirect
	github.com/shurcooL/sanitized_anchor_name v1.0.0 // indirect
	github.com/sillyousu/goid v0.0.0-20170828045124-4c1c17f636f6 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/cast v1.4.1 // indirect
	github.com/tidwall/gjson v1.14.4 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/tmdvs/Go-Emoji-Utils v1.1.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/uber/jaeger-client-go v2.29.1+incompatible // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/vmihailenco/msgpack v4.0.4+incompatible // indirect
	go.etcd.io/etcd v0.0.0-**************-3cf2f69b5738 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/mock v0.5.0 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/crypto v0.29.0 // indirect
	golang.org/x/exp v0.0.0-20240506185415-9bf2ced13842 // indirect
	golang.org/x/mod v0.18.0 // indirect
	golang.org/x/net v0.31.0 // indirect
	golang.org/x/sys v0.27.0 // indirect
	golang.org/x/tools v0.22.0 // indirect
	golang.org/x/xerrors v0.0.0-20231012003039-104605ab7028 // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto v0.0.0-20240123012728-ef4313101c80 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240123012728-ef4313101c80 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240123012728-ef4313101c80 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	moul.io/http2curl v1.0.0 // indirect
	pgregory.net/rand v1.0.2 // indirect
)

require (
	git.garena.com/shopee/o2o-intelligence/search/nlp v0.0.0-20240528070402-d42df431af5e
	git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib v0.0.0-20240809021529-788b88cf5476
	git.garena.com/shopee/platform/tracing v1.12.0
	github.com/redis/go-redis/v9 v9.0.3
)

replace (
	git.garena.com/common/gocommon => git.garena.com/common/gocommon v0.0.0-20171208043917-c85783eb4c27
	git.garena.com/shopee/devops/golang_aegislib => git.garena.com/shopee/devops/golang_aegislib v0.0.10
	git.garena.com/shopee/feed/comm_lib => git.garena.com/shopee/feed/comm_lib v1.1.20-0.20250317062115-6fbe669ab41b
	git.garena.com/shopee/feed/ginweb => git.garena.com/shopee/feed/ginweb v1.1.9
	git.garena.com/shopee/feed/microkit => git.garena.com/shopee/feed/microkit v1.1.7-0.20220323110909-48582084f597
	git.garena.com/shopee/foody/service => git.garena.com/shopee/foody/service v1.56.1-061001-hotfix-delivery-panic.0.20240528030040-bf50607b605a
	git.garena.com/shopee/log-server => git.garena.com/shopee/o2o-intelligence/common/common-lib/3rdparty/log-server v0.0.0-20211101100850-e060c7f083aa
	git.garena.com/shopee/marketing/config-client => git.garena.com/shopee/o2o-intelligence/common/common-lib/3rdparty/shopee/config-client v0.0.0-**************-f5b58dd0dfc5
	git.garena.com/shopee/platform/service-governance/observability/metric => git.garena.com/shopee/platform/service-governance/observability/metric v1.0.16

	github.com/BurntSushi/toml => github.com/BurntSushi/toml v0.3.1
	github.com/coreos/etcd => go.etcd.io/etcd v0.0.0-**************-3cf2f69b5738
	github.com/leekchan/accounting => github.com/leekchan/accounting v0.3.1
	github.com/micro/go-micro => git.garena.com/shopee/o2o-intelligence/common/common-lib/3rdparty/go-micro-fix-quic-1.16.0 v0.0.0-**************-d4bad8bd7b82
	github.com/micro/h2c v1.0.0 => golang.org/x/net v0.0.0-**************-ab342639438
	github.com/micro/mdns => github.com/p2pNG/mdns v0.0.0-**************-aaa81c17b902
	github.com/micro/protoc-gen-micro => github.com/megaherz/protoc-gen-micro v0.3.1-0.**************-f78c2be7424f

	github.com/prometheus/client_golang => github.com/prometheus/client_golang v1.13.0
	github.com/prometheus/client_model => github.com/prometheus/client_model v0.2.0
	github.com/prometheus/common => github.com/prometheus/common v0.37.0
	github.com/prometheus/procfs => github.com/prometheus/procfs v0.8.0
	github.com/sillyousu/goid => github.com/multippt/goid v0.0.0-**************-afd1d7cece87
	go.etcd.io/etcd => github.com/coreos/etcd v0.0.0-**************-3cf2f69b5738
	go.opentelemetry.io/otel => go.opentelemetry.io/otel v0.14.0
	golang.org/x/crypto => golang.org/x/crypto v0.18.0
	golang.org/x/sync => golang.org/x/sync v0.6.0
	google.golang.org/grpc => git.garena.com/shopee/o2o-intelligence/common/common-lib/3rdparty/grpc-go-v1_26_0 v0.0.0-20220714100447-6c563bbe4722 // 解决gorpc@v1.26.0/examples和gorpc/examples@1.26.0依赖问题
	google.golang.org/grpc/examples/helloworld/helloworld => google.golang.org/grpc/examples/helloworld/helloworld v1.43.0
	google.golang.org/protobuf => google.golang.org/protobuf v1.33.0
)
